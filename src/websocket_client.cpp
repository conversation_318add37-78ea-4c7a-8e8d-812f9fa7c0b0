// Windows header order fix - must be before any other Windows headers
#if defined(_WIN32)
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#include "websocket_client.h"
#include <iostream>
#include <chrono>
#include <cstring>

// Static protocol definition
const char* WebSocketClient::protocolName_ = "piano-protocol";

struct lws_protocols WebSocketClient::protocols_[] = {
    {
        WebSocketClient::protocolName_,
        WebSocketClient::lwsCallback,
        0,
        4096,
        0, nullptr, 0
    },
    { nullptr, nullptr, 0, 0, 0, nullptr, 0 } // terminator
};

WebSocketClient::WebSocketClient()
    : connected_(false)
    , shouldStop_(false)
    , context_(nullptr)
    , wsi_(nullptr)
    , lastSendTime_(std::chrono::steady_clock::now())
{
}

WebSocketClient::~WebSocketClient() {
    disconnect();
}

bool WebSocketClient::connect(const std::string& host, int port) {
    // Disconnect any existing connection first
    if (connected_ || context_ || wsi_) {
        disconnect();
    }
    
    // Create LWS context
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));
    
    info.port = CONTEXT_PORT_NO_LISTEN;
    info.protocols = protocols_;
    info.gid = -1;
    info.uid = -1;
    info.user = this;
    
    context_ = lws_create_context(&info);
    if (!context_) {
        return false;
    }
    
    // Create connection
    struct lws_client_connect_info ccinfo;
    memset(&ccinfo, 0, sizeof(ccinfo));
    
    ccinfo.context = context_;
    ccinfo.address = host.c_str();
    ccinfo.port = port;
    ccinfo.path = "/";
    ccinfo.host = ccinfo.address;
    ccinfo.origin = ccinfo.address;
    ccinfo.protocol = protocolName_;
    ccinfo.userdata = this;
    
    wsi_ = lws_client_connect_via_info(&ccinfo);
    if (!wsi_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        return false;
    }
    
    // Start WebSocket service thread (handles network I/O only)
    shouldStop_ = false;
    serviceThread_ = std::make_unique<std::thread>([this]() {
        while (!shouldStop_) {
            // Process WebSocket events with minimal timeout
            lws_service(context_, 1); // 1ms timeout for network I/O

            // Short sleep to prevent excessive CPU usage
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    });

    // Start message processing thread (handles incoming message processing)
    messageProcessThread_ = std::make_unique<std::thread>([this]() {
        while (!shouldStop_) {
            std::unique_lock<std::mutex> lock(messageMutex_);

            // Wait for messages or stop signal
            messageCondition_.wait(lock, [this] {
                return !incomingMessages_.empty() || shouldStop_;
            });

            // Process all available messages
            while (!incomingMessages_.empty() && !shouldStop_) {
                std::string message = incomingMessages_.front();
                incomingMessages_.pop();

                // Release lock while processing message
                lock.unlock();
                handleMessage(message);
                lock.lock();
            }
        }
    });
    
    return true;
}

void WebSocketClient::disconnect() {
    // Set stop flag first
    shouldStop_ = true;
    connected_ = false;

    // Close WebSocket connection properly
    if (wsi_) {
        // Request connection close
        lws_close_reason(wsi_, LWS_CLOSE_STATUS_NORMAL, nullptr, 0);
        wsi_ = nullptr;
    }

    // Wake up service thread to process the close
    if (context_) {
        lws_cancel_service(context_);
    }

    // Wake up message processing thread
    messageCondition_.notify_all();

    // Wait for both threads to finish
    if (serviceThread_ && serviceThread_->joinable()) {
        serviceThread_->join();
        serviceThread_.reset();
    }

    if (messageProcessThread_ && messageProcessThread_->joinable()) {
        messageProcessThread_->join();
        messageProcessThread_.reset();
    }

    // Clean up context
    if (context_) {
        lws_context_destroy(context_);
        context_ = nullptr;
    }

    // Clear message queues
    {
        std::lock_guard<std::mutex> lock(messageMutex_);
        while (!incomingMessages_.empty()) {
            incomingMessages_.pop();
        }
    }

    {
        std::lock_guard<std::mutex> lock(outgoingMutex_);
        while (!outgoingMessages_.empty()) {
            outgoingMessages_.pop();
        }
    }
}

bool WebSocketClient::isConnected() const {
    return connected_;
}

bool WebSocketClient::sendMidi(const std::vector<MidiBuffer>& buffers) {
    if (!connected_ || buffers.empty()) {
        return false;
    }

    // Limit the number of buffers per message to prevent large messages
    // Increased from 10 to 50 for better chord/simultaneous note handling
    const size_t maxBuffersPerMessage = 50;

    // Split large buffer arrays into smaller chunks
    for (size_t i = 0; i < buffers.size(); i += maxBuffersPerMessage) {
        size_t end = std::min(i + maxBuffersPerMessage, buffers.size());
        std::vector<MidiBuffer> chunk(buffers.begin() + i, buffers.begin() + end);

        nlohmann::json message;
        message["type"] = "midi";

        // Convert MidiBuffer structs to arrays
        nlohmann::json buffersArray = nlohmann::json::array();
        for (const auto& buffer : chunk) {
            nlohmann::json bufferArray = nlohmann::json::array();
            bufferArray.push_back(buffer.status);
            bufferArray.push_back(buffer.note);
            bufferArray.push_back(buffer.velocity);
            bufferArray.push_back(buffer.color_r);
            bufferArray.push_back(buffer.color_g);
            bufferArray.push_back(buffer.color_b);
            bufferArray.push_back(buffer.delta_hi);
            bufferArray.push_back(buffer.delta_lo);
            buffersArray.push_back(bufferArray);
        }

        message["buffers"] = buffersArray;

        // Queue each chunk separately
        queueMessage(message);
    }

    return true;
}

bool WebSocketClient::sendMidi(const MidiBuffer& buffer) {
    return sendMidi(std::vector<MidiBuffer>{buffer});
}

bool WebSocketClient::sendNoteOn(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 144; // Note On
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::sendNoteOff(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 128; // Note Off
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::updateUsername(const std::string& username) {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "update_username";
    message["username"] = username;
    
    return sendMessage(message);
}

bool WebSocketClient::requestUsers() {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "get_users";
    
    return sendMessage(message);
}

bool WebSocketClient::sendPing() {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "ping";
    
    return sendMessage(message);
}

// Callback setters
void WebSocketClient::setConnectedCallback(ConnectedCallback callback) {
    connectedCallback_ = callback;
}

void WebSocketClient::setMidiCallback(MidiCallback callback) {
    midiCallback_ = callback;
}

void WebSocketClient::setUsernameUpdatedCallback(UsernameUpdatedCallback callback) {
    usernameUpdatedCallback_ = callback;
}

void WebSocketClient::setUserUpdateCallback(UserUpdateCallback callback) {
    userUpdateCallback_ = callback;
}

void WebSocketClient::setErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void WebSocketClient::setPongCallback(PongCallback callback) {
    pongCallback_ = callback;
}

bool WebSocketClient::sendMessage(const nlohmann::json& message) {
    // Queue the message for thread-safe sending
    queueMessage(message);
    return true;
}

void WebSocketClient::queueMessage(const nlohmann::json& message) {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    // Limit queue size to prevent memory issues
    // Increased from 1000 to 5000 for better handling of rapid note sequences
    const size_t maxQueueSize = 5000;
    if (outgoingMessages_.size() >= maxQueueSize) {
        // Drop oldest messages if queue is full
        while (outgoingMessages_.size() >= maxQueueSize) {
            outgoingMessages_.pop();
        }
    }

    outgoingMessages_.push(message.dump());

    // Request writable callback to process the queue
    if (connected_ && wsi_) {
        lws_callback_on_writable(wsi_);
    }
}

bool WebSocketClient::sendMessageDirect(const std::string& messageStr) {
    if (!connected_ || !wsi_) {
        return false;
    }

    size_t len = messageStr.length();

    // Check if message is too large
    if (len > 32768) { // 32KB limit
        // Split large messages or reject them
        return false;
    }

    // Allocate buffer with LWS_PRE padding
    std::vector<unsigned char> buffer(LWS_PRE + len);
    memcpy(&buffer[LWS_PRE], messageStr.c_str(), len);

    int result = lws_write(wsi_, &buffer[LWS_PRE], len, LWS_WRITE_TEXT);

    return result >= 0;
}



void WebSocketClient::processOutgoingQueue() {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::microseconds>(now - lastSendTime_);

    // Reduced rate limiting for better real-time performance
    // Allow more frequent sends for immediate response
    if (timeSinceLastSend.count() < 500) { // 500 microseconds = 0.5ms
        return;
    }

    // Process messages in very small batches to not block incoming message processing
    int processed = 0;
    const int maxPerCall = 5; // Small batch size to prioritize incoming messages

    while (!outgoingMessages_.empty() && processed < maxPerCall) {
        std::string message = outgoingMessages_.front();
        outgoingMessages_.pop();

        // Release lock temporarily to send message
        outgoingMutex_.unlock();
        bool success = sendMessageDirect(message);
        outgoingMutex_.lock();

        if (success) {
            lastSendTime_ = std::chrono::steady_clock::now();
            processed++;

            // No additional delay - let WebSocket handle the flow control
        } else {
            // If send failed, put message back at front of queue
            std::queue<std::string> temp;
            temp.push(message);
            while (!outgoingMessages_.empty()) {
                temp.push(outgoingMessages_.front());
                outgoingMessages_.pop();
            }
            outgoingMessages_ = temp;
            break;
        }
    }
}

void WebSocketClient::handleMessage(const std::string& message) {
    try {
        nlohmann::json data = nlohmann::json::parse(message);

        if (!data.contains("type")) {
            return;
        }

        std::string type = data["type"];

        if (type == "connected") {
            if (data.contains("clientId") && data.contains("user")) {
                clientId_ = data["clientId"];

                auto userObj = data["user"];
                currentUser_.id = userObj["id"];
                currentUser_.username = userObj["username"];

                // Parse active users
                activeUsers_.clear();
                if (data.contains("activeUsers")) {
                    for (const auto& userJson : data["activeUsers"]) {
                        UserInfo user;
                        user.id = userJson["id"];
                        user.username = userJson["username"];
                        user.connectedClients = userJson.value("connectedClients", 1);
                        activeUsers_.push_back(user);
                    }
                }

                if (connectedCallback_) {
                    connectedCallback_(clientId_, currentUser_, activeUsers_);
                }
            }
        }
        else if (type == "midi") {
            if (data.contains("buffers") && data.contains("fromUser")) {
                std::vector<MidiBuffer> buffers;

                for (const auto& bufferJson : data["buffers"]) {
                    if (bufferJson.size() >= 8) {
                        MidiBuffer buffer;
                        buffer.status = bufferJson[0];
                        buffer.note = bufferJson[1];
                        buffer.velocity = bufferJson[2];
                        buffer.color_r = bufferJson[3];
                        buffer.color_g = bufferJson[4];
                        buffer.color_b = bufferJson[5];
                        buffer.delta_hi = bufferJson[6];
                        buffer.delta_lo = bufferJson[7];
                        buffers.push_back(buffer);
                    }
                }

                UserInfo fromUser;
                auto fromUserObj = data["fromUser"];
                fromUser.id = fromUserObj["id"];
                fromUser.username = fromUserObj["username"];

                if (midiCallback_ && !buffers.empty()) {
                    midiCallback_(buffers, fromUser);
                }
            }
        }
        else if (type == "username_updated") {
            if (data.contains("username")) {
                std::string newUsername = data["username"];
                currentUser_.username = newUsername;

                if (usernameUpdatedCallback_) {
                    usernameUpdatedCallback_(newUsername);
                }
            }
        }
        else if (type == "user_update") {
            if (data.contains("users")) {
                activeUsers_.clear();
                for (const auto& userJson : data["users"]) {
                    UserInfo user;
                    user.id = userJson["id"];
                    user.username = userJson["username"];
                    user.connectedClients = userJson.value("connectedClients", 1);
                    activeUsers_.push_back(user);
                }

                if (userUpdateCallback_) {
                    userUpdateCallback_(activeUsers_);
                }
            }
        }
        else if (type == "error") {
            if (data.contains("message")) {
                std::string errorMessage = data["message"];

                if (errorCallback_) {
                    errorCallback_(errorMessage);
                }
            }
        }
        else if (type == "pong") {
            if (pongCallback_) {
                pongCallback_();
            }
        }
    }
    catch (const std::exception& e) {
        if (errorCallback_) {
            errorCallback_("Failed to parse message: " + std::string(e.what()));
        }
    }
}

int WebSocketClient::lwsCallback(struct lws* wsi, enum lws_callback_reasons reason,
                                void* user, void* in, size_t len) {
    WebSocketClient* client = static_cast<WebSocketClient*>(lws_context_user(lws_get_context(wsi)));

    switch (reason) {
        case LWS_CALLBACK_CLIENT_ESTABLISHED:
            client->connected_ = true;
            break;

        case LWS_CALLBACK_CLIENT_RECEIVE:
            if (client && in && len > 0) {
                std::string message(static_cast<char*>(in), len);

                {
                    std::lock_guard<std::mutex> lock(client->messageMutex_);

                    // Increased queue size for better buffering
                    const size_t maxIncomingQueueSize = 100;
                    if (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                        // Remove oldest messages if queue is full
                        while (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                            client->incomingMessages_.pop();
                        }
                    }

                    client->incomingMessages_.push(message);
                }

                // Wake up message processing thread immediately
                client->messageCondition_.notify_one();
            }
            break;

        case LWS_CALLBACK_CLIENT_CONNECTION_ERROR:
            client->connected_ = false;
            client->wsi_ = nullptr;
            if (client->errorCallback_) {
                client->errorCallback_("Connection error");
            }
            break;

        case LWS_CALLBACK_CLOSED:
            client->connected_ = false;
            client->wsi_ = nullptr;
            break;

        case LWS_CALLBACK_CLIENT_WRITEABLE:
            // Process outgoing message queue when socket is writable
            if (client) {
                client->processOutgoingQueue();

                // Request callback again if there are more messages
                {
                    std::lock_guard<std::mutex> lock(client->outgoingMutex_);
                    if (!client->outgoingMessages_.empty()) {
                        lws_callback_on_writable(wsi);
                    }
                }
            }
            break;

        default:
            break;
    }

    return 0;
}
