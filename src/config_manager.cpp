#include "config_manager.h"
#include "soundfont_manager.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#else
#include <unistd.h>
#include <pwd.h>
#include <sys/stat.h>
#endif

// Simple JSON-like parser and serializer
// Note: This is a minimal implementation. For production, consider using a proper JSON library
namespace SimpleJSON {
    std::string EscapeString(const std::string& str) {
        std::string escaped;
        for (char c : str) {
            switch (c) {
                case '"': escaped += "\\\""; break;
                case '\\': escaped += "\\\\"; break;
                case '\n': escaped += "\\n"; break;
                case '\r': escaped += "\\r"; break;
                case '\t': escaped += "\\t"; break;
                default: escaped += c; break;
            }
        }
        return escaped;
    }



    std::string UnescapeString(const std::string& str) {
        std::string unescaped;
        for (size_t i = 0; i < str.length(); ++i) {
            if (str[i] == '\\' && i + 1 < str.length()) {
                switch (str[i + 1]) {
                    case '"': unescaped += '"'; i++; break;
                    case '\\': unescaped += '\\'; i++; break;
                    case 'n': unescaped += '\n'; i++; break;
                    case 'r': unescaped += '\r'; i++; break;
                    case 't': unescaped += '\t'; i++; break;
                    default: unescaped += str[i]; break;
                }
            } else {
                unescaped += str[i];
            }
        }
        return unescaped;
    }



    // Extract value from JSON string (simple implementation)
    std::string GetStringValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return "";

        pos = json.find(":", pos);
        if (pos == std::string::npos) return "";

        pos = json.find("\"", pos);
        if (pos == std::string::npos) return "";
        pos++; // Skip opening quote

        // Find the closing quote, handling escaped quotes
        size_t end = pos;
        while (end < json.length()) {
            if (json[end] == '"') {
                // Check if this quote is escaped
                size_t backslash_count = 0;
                size_t check_pos = end - 1;
                while (check_pos < json.length() && json[check_pos] == '\\') {
                    backslash_count++;
                    if (check_pos == 0) break;
                    check_pos--;
                }
                // If even number of backslashes (including 0), quote is not escaped
                if (backslash_count % 2 == 0) {
                    break;
                }
            }
            end++;
        }

        if (end >= json.length()) return "";

        return UnescapeString(json.substr(pos, end - pos));
    }



    float GetFloatValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return 0.0f;
        
        pos = json.find(":", pos);
        if (pos == std::string::npos) return 0.0f;
        
        // Skip whitespace
        while (pos < json.length() && (json[pos] == ':' || json[pos] == ' ' || json[pos] == '\t')) pos++;
        
        size_t end = pos;
        while (end < json.length() && (std::isdigit(json[end]) || json[end] == '.' || json[end] == '-')) end++;
        
        if (end > pos) {
            return std::stof(json.substr(pos, end - pos));
        }
        return 0.0f;
    }

    int GetIntValue(const std::string& json, const std::string& key) {
        return static_cast<int>(GetFloatValue(json, key));
    }

    bool GetBoolValue(const std::string& json, const std::string& key) {
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return false;

        pos = json.find(":", pos);
        if (pos == std::string::npos) return false;

        return json.find("true", pos) < json.find("false", pos);
    }

    std::vector<std::string> GetStringArrayValue(const std::string& json, const std::string& key) {
        std::vector<std::string> result;
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return result;

        pos = json.find(":", pos);
        if (pos == std::string::npos) return result;

        pos = json.find("[", pos);
        if (pos == std::string::npos) return result;
        pos++; // Skip opening bracket

        size_t end = json.find("]", pos);
        if (end == std::string::npos) return result;

        std::string array_content = json.substr(pos, end - pos);

        // Parse array elements
        size_t element_pos = 0;
        while (element_pos < array_content.length()) {
            size_t quote_start = array_content.find("\"", element_pos);
            if (quote_start == std::string::npos) break;
            quote_start++; // Skip opening quote

            size_t quote_end = array_content.find("\"", quote_start);
            if (quote_end == std::string::npos) break;

            result.push_back(UnescapeString(array_content.substr(quote_start, quote_end - quote_start)));
            element_pos = quote_end + 1;
        }

        return result;
    }

    std::vector<bool> GetBoolArrayValue(const std::string& json, const std::string& key) {
        std::vector<bool> result;
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return result;

        pos = json.find(":", pos);
        if (pos == std::string::npos) return result;

        pos = json.find("[", pos);
        if (pos == std::string::npos) return result;
        pos++; // Skip opening bracket

        size_t end = json.find("]", pos);
        if (end == std::string::npos) return result;

        std::string array_content = json.substr(pos, end - pos);

        // Parse array elements
        size_t element_pos = 0;
        while (element_pos < array_content.length()) {
            size_t true_pos = array_content.find("true", element_pos);
            size_t false_pos = array_content.find("false", element_pos);

            if (true_pos != std::string::npos && (false_pos == std::string::npos || true_pos < false_pos)) {
                result.push_back(true);
                element_pos = true_pos + 4;
            } else if (false_pos != std::string::npos) {
                result.push_back(false);
                element_pos = false_pos + 5;
            } else {
                break;
            }
        }

        return result;
    }

    std::vector<int> GetIntArrayValue(const std::string& json, const std::string& key) {
        std::vector<int> result;
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return result;

        pos = json.find(":", pos);
        if (pos == std::string::npos) return result;

        pos = json.find("[", pos);
        if (pos == std::string::npos) return result;
        pos++; // Skip opening bracket

        size_t end = json.find("]", pos);
        if (end == std::string::npos) return result;

        std::string array_content = json.substr(pos, end - pos);

        // Parse array elements
        std::stringstream ss(array_content);
        std::string element;
        while (std::getline(ss, element, ',')) {
            // Remove whitespace
            element.erase(0, element.find_first_not_of(" \t\n\r"));
            element.erase(element.find_last_not_of(" \t\n\r") + 1);

            if (!element.empty() && std::isdigit(element[0]) || element[0] == '-') {
                result.push_back(std::stoi(element));
            }
        }

        return result;
    }

    std::vector<float> GetFloatArrayValue(const std::string& json, const std::string& key) {
        std::vector<float> result;
        std::string search = "\"" + key + "\"";
        size_t pos = json.find(search);
        if (pos == std::string::npos) return result;

        pos = json.find(":", pos);
        if (pos == std::string::npos) return result;

        pos = json.find("[", pos);
        if (pos == std::string::npos) return result;
        pos++; // Skip opening bracket

        size_t end = json.find("]", pos);
        if (end == std::string::npos) return result;

        std::string array_content = json.substr(pos, end - pos);

        // Parse array elements
        std::stringstream ss(array_content);
        std::string element;
        while (std::getline(ss, element, ',')) {
            // Remove whitespace
            element.erase(0, element.find_first_not_of(" \t\n\r"));
            element.erase(element.find_last_not_of(" \t\n\r") + 1);

            if (!element.empty() && (std::isdigit(element[0]) || element[0] == '-' || element[0] == '.')) {
                result.push_back(std::stof(element));
            }
        }

        return result;
    }
}

ConfigManager::ConfigManager() 
    : auto_save_enabled_(true), config_dirty_(false) {
}

ConfigManager::~ConfigManager() {
    if (auto_save_enabled_ && config_dirty_) {
        SaveConfig();
    }
}

bool ConfigManager::Initialize(const std::string& config_file_path) {
    if (config_file_path.empty()) {
        config_file_path_ = GetDefaultConfigPath();
    } else {
        config_file_path_ = config_file_path;
    }

    std::cout << "Config file path: " << config_file_path_ << std::endl;

    // Create config directory if it doesn't exist
    std::string config_dir = std::filesystem::path(config_file_path_).parent_path().string();
    if (!CreateDirectoryIfNotExists(config_dir)) {
        std::cerr << "Failed to create config directory: " << config_dir << std::endl;
        return false;
    }

    // Load existing config or create default
    if (!LoadConfig()) {
        std::cout << "No existing config found, using defaults" << std::endl;
        ResetToDefaults();
        SaveConfig(); // Save default config
    }

    return true;
}

bool ConfigManager::LoadConfig() {
    std::string content;
    if (!ReadFile(config_file_path_, content)) {
        return false;
    }

    return LoadFromJSON(content);
}

bool ConfigManager::SaveConfig() {
    std::string json_content = SaveToJSON();
    if (WriteFile(config_file_path_, json_content)) {
        config_dirty_ = false;
        return true;
    }
    return false;
}

void ConfigManager::AutoSave() {
    if (auto_save_enabled_) {
        MarkDirty();
        SaveConfig();
    }
}

void ConfigManager::ResetToDefaults() {
    config_ = AppConfig(); // Reset to default values
    config_dirty_ = true;
}

std::string ConfigManager::GetDefaultConfigPath() {
    std::string config_dir = GetConfigDirectory();
    return config_dir + "/pianowo_config.json";
}

std::string ConfigManager::GetConfigDirectory() {
#ifdef _WIN32
    char path[MAX_PATH];
    if (SUCCEEDED(SHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, path))) {
        return std::string(path) + "/pianowo";
    }
    return "./config";
#else
    const char* home = getenv("HOME");
    if (!home) {
        struct passwd* pw = getpwuid(getuid());
        if (pw) home = pw->pw_dir;
    }
    if (home) {
        return std::string(home) + "/.config/pianowo";
    }
    return "./config";
#endif
}

std::string ConfigManager::GetExecutableDirectory() {
#ifdef _WIN32
    char path[MAX_PATH];
    GetModuleFileNameA(NULL, path, MAX_PATH);
    std::string exe_path(path);
    return exe_path.substr(0, exe_path.find_last_of("\\/"));
#else
    char path[1024];
    ssize_t len = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (len != -1) {
        path[len] = '\0';
        std::string exe_path(path);
        return exe_path.substr(0, exe_path.find_last_of("/"));
    }
    return ".";
#endif
}

bool ConfigManager::ReadFile(const std::string& file_path, std::string& content) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        return false;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    content = buffer.str();
    return true;
}

bool ConfigManager::WriteFile(const std::string& file_path, const std::string& content) {
    std::ofstream file(file_path);
    if (!file.is_open()) {
        return false;
    }

    file << content;
    return file.good();
}

bool ConfigManager::CreateDirectoryIfNotExists(const std::string& dir_path) {
    try {
        std::filesystem::create_directories(dir_path);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Failed to create directory: " << e.what() << std::endl;
        return false;
    }
}

bool ConfigManager::LoadFromJSON(const std::string& json_content) {
    try {
        // Parse keyboard settings
        config_.keyboard.auto_layout = SimpleJSON::GetBoolValue(json_content, "keyboard_auto_layout");
        config_.keyboard.keyboard_margin = SimpleJSON::GetFloatValue(json_content, "keyboard_margin");
        config_.keyboard.white_key_width = SimpleJSON::GetFloatValue(json_content, "white_key_width");
        config_.keyboard.white_key_height = SimpleJSON::GetFloatValue(json_content, "white_key_height");
        config_.keyboard.black_key_width = SimpleJSON::GetFloatValue(json_content, "black_key_width");
        config_.keyboard.black_key_height = SimpleJSON::GetFloatValue(json_content, "black_key_height");

        // Parse audio settings
        config_.audio.audio_enabled = SimpleJSON::GetBoolValue(json_content, "audio_enabled");
        config_.audio.volume = SimpleJSON::GetFloatValue(json_content, "audio_volume");
        config_.audio.soundfont_path = SimpleJSON::GetStringValue(json_content, "soundfont_path");
        config_.audio.polyphony = SimpleJSON::GetIntValue(json_content, "audio_polyphony");
        config_.audio.limiter_enabled = SimpleJSON::GetBoolValue(json_content, "limiter_enabled");

        // Parse audio limiter settings
        config_.audio.limiter_threshold = SimpleJSON::GetFloatValue(json_content, "limiter_threshold");
        config_.audio.limiter_release_time = SimpleJSON::GetFloatValue(json_content, "limiter_release_time");
        config_.audio.limiter_lookahead_time = SimpleJSON::GetFloatValue(json_content, "limiter_lookahead_time");

        // Parse BASS FX settings
        config_.audio.bassfx_enabled = SimpleJSON::GetBoolValue(json_content, "bassfx_enabled");
        config_.audio.bassfx_reverb_enabled = SimpleJSON::GetBoolValue(json_content, "bassfx_reverb_enabled");
        config_.audio.bassfx_chorus_enabled = SimpleJSON::GetBoolValue(json_content, "bassfx_chorus_enabled");
        config_.audio.bassfx_echo_enabled = SimpleJSON::GetBoolValue(json_content, "bassfx_echo_enabled");
        config_.audio.bassfx_compressor_enabled = SimpleJSON::GetBoolValue(json_content, "bassfx_compressor_enabled");
        config_.audio.bassfx_reverb_mix = SimpleJSON::GetFloatValue(json_content, "bassfx_reverb_mix");
        config_.audio.bassfx_chorus_mix = SimpleJSON::GetFloatValue(json_content, "bassfx_chorus_mix");
        config_.audio.bassfx_echo_mix = SimpleJSON::GetFloatValue(json_content, "bassfx_echo_mix");
        config_.audio.bassfx_compressor_ratio = SimpleJSON::GetFloatValue(json_content, "bassfx_compressor_ratio");

        // Parse multiple soundfont settings
        config_.audio.use_multiple_soundfonts = SimpleJSON::GetBoolValue(json_content, "use_multiple_soundfonts");
        config_.audio.soundfont_paths = SimpleJSON::GetStringArrayValue(json_content, "soundfont_paths");
        config_.audio.soundfont_enabled = SimpleJSON::GetBoolArrayValue(json_content, "soundfont_enabled");
        config_.audio.soundfont_priorities = SimpleJSON::GetIntArrayValue(json_content, "soundfont_priorities");
        config_.audio.soundfont_volumes = SimpleJSON::GetFloatArrayValue(json_content, "soundfont_volumes");
        config_.audio.soundfont_should_load = SimpleJSON::GetBoolArrayValue(json_content, "soundfont_should_load");

        // Parse display settings
        config_.display.background_color[0] = SimpleJSON::GetFloatValue(json_content, "bg_color_r");
        config_.display.background_color[1] = SimpleJSON::GetFloatValue(json_content, "bg_color_g");
        config_.display.background_color[2] = SimpleJSON::GetFloatValue(json_content, "bg_color_b");
        config_.display.background_mode = SimpleJSON::GetIntValue(json_content, "background_mode");
        // Convert old transparent mode (2) to image mode (2) and shift image mode to (2)
        if (config_.display.background_mode == 2) {
            config_.display.background_mode = 1; // Change to radial gradient
        } else if (config_.display.background_mode == 3) {
            config_.display.background_mode = 2; // Image mode is now 2
        }
        config_.display.gradient_center_color[0] = SimpleJSON::GetFloatValue(json_content, "gradient_center_r");
        config_.display.gradient_center_color[1] = SimpleJSON::GetFloatValue(json_content, "gradient_center_g");
        config_.display.gradient_center_color[2] = SimpleJSON::GetFloatValue(json_content, "gradient_center_b");
        config_.display.gradient_edge_color[0] = SimpleJSON::GetFloatValue(json_content, "gradient_edge_r");
        config_.display.gradient_edge_color[1] = SimpleJSON::GetFloatValue(json_content, "gradient_edge_g");
        config_.display.gradient_edge_color[2] = SimpleJSON::GetFloatValue(json_content, "gradient_edge_b");

        config_.display.background_image_path = SimpleJSON::GetStringValue(json_content, "background_image_path");
        config_.display.background_image_opacity = SimpleJSON::GetFloatValue(json_content, "background_image_opacity");
        config_.display.background_image_scale_mode = SimpleJSON::GetIntValue(json_content, "background_image_scale_mode");
        config_.display.show_settings = SimpleJSON::GetBoolValue(json_content, "show_settings");
        config_.display.show_debug = SimpleJSON::GetBoolValue(json_content, "show_debug");
        config_.display.show_bassmidi_status = SimpleJSON::GetBoolValue(json_content, "show_bassmidi_status");
        config_.display.show_midi_input = SimpleJSON::GetBoolValue(json_content, "show_midi_input");
        config_.display.show_audio_limiter = SimpleJSON::GetBoolValue(json_content, "show_audio_limiter");
        config_.display.show_soundfont_manager = SimpleJSON::GetBoolValue(json_content, "show_soundfont_manager");
        config_.display.show_notes_count_status = SimpleJSON::GetBoolValue(json_content, "show_notes_count_status");
        config_.display.show_bassmidi_status_overlay = SimpleJSON::GetBoolValue(json_content, "show_bassmidi_status_overlay");

        // Parse note indicator settings
        config_.display.note_indicator_enabled = SimpleJSON::GetBoolValue(json_content, "note_indicator_enabled");
        config_.display.note_indicator_color[0] = SimpleJSON::GetFloatValue(json_content, "note_indicator_color_r");
        config_.display.note_indicator_color[1] = SimpleJSON::GetFloatValue(json_content, "note_indicator_color_g");
        config_.display.note_indicator_color[2] = SimpleJSON::GetFloatValue(json_content, "note_indicator_color_b");
        config_.display.note_indicator_color[3] = SimpleJSON::GetFloatValue(json_content, "note_indicator_color_a");
        config_.display.note_indicator_size[0] = SimpleJSON::GetFloatValue(json_content, "note_indicator_size_w");
        config_.display.note_indicator_size[1] = SimpleJSON::GetFloatValue(json_content, "note_indicator_size_h");
        config_.display.note_indicator_position[0] = SimpleJSON::GetFloatValue(json_content, "note_indicator_position_x");
        config_.display.note_indicator_position[1] = SimpleJSON::GetFloatValue(json_content, "note_indicator_position_y");
        config_.display.note_indicator_animation_duration = SimpleJSON::GetFloatValue(json_content, "note_indicator_animation_duration");
        config_.display.note_indicator_bounce_distance = SimpleJSON::GetFloatValue(json_content, "note_indicator_bounce_distance");

        // Parse MIDI settings
        config_.midi.selected_midi_device = SimpleJSON::GetIntValue(json_content, "selected_midi_device");
        config_.midi.selected_alsa_midi_device = SimpleJSON::GetIntValue(json_content, "selected_alsa_midi_device");
        config_.midi.use_alsa_midi = SimpleJSON::GetBoolValue(json_content, "use_alsa_midi");
        config_.midi.auto_open_midi = SimpleJSON::GetBoolValue(json_content, "auto_open_midi");

        // Parse External Process MIDI settings
        config_.midi.use_ext_process_midi = SimpleJSON::GetBoolValue(json_content, "use_ext_process_midi");
        config_.midi.ext_process_executable_path = SimpleJSON::GetStringValue(json_content, "ext_process_executable_path");
        // Debug: Show the relevant part of JSON
        size_t args_pos = json_content.find("\"ext_process_arguments\":");
        if (args_pos != std::string::npos) {
            size_t line_start = json_content.rfind('\n', args_pos);
            size_t line_end = json_content.find('\n', args_pos);
            if (line_start == std::string::npos) line_start = 0;
            if (line_end == std::string::npos) line_end = json_content.length();
            //std::string line = json_content.substr(line_start, line_end - line_start);
            //std::cout << "DEBUG: JSON line: " << line << std::endl;
        }

        std::string loaded_args = SimpleJSON::GetStringValue(json_content, "ext_process_arguments");
        //std::cout << "DEBUG: Loaded args from JSON: [" << loaded_args << "]" << std::endl;
        config_.midi.ext_process_arguments = loaded_args;
        config_.midi.ext_process_enabled = SimpleJSON::GetBoolValue(json_content, "ext_process_enabled");
        config_.midi.color_type = SimpleJSON::GetIntValue(json_content, "midi_color_type");

        // Parse window settings
        config_.window.width = SimpleJSON::GetIntValue(json_content, "window_width");
        config_.window.height = SimpleJSON::GetIntValue(json_content, "window_height");
        config_.window.maximized = SimpleJSON::GetBoolValue(json_content, "window_maximized");


        // Parse cheat tool settings
        config_.cheat_tool.show_cheat_tool = SimpleJSON::GetBoolValue(json_content, "show_cheat_tool");
        config_.cheat_tool.multioctave_enabled = SimpleJSON::GetBoolValue(json_content, "multioctave_enabled");
        config_.cheat_tool.multioctave_count = SimpleJSON::GetIntValue(json_content, "multioctave_count");
        config_.cheat_tool.multioctave_distance = SimpleJSON::GetIntValue(json_content, "multioctave_distance");
        config_.cheat_tool.pc_keyboard_transpose = SimpleJSON::GetIntValue(json_content, "pc_keyboard_transpose");
        config_.cheat_tool.pc_keyboard_octave = SimpleJSON::GetIntValue(json_content, "pc_keyboard_octave");

        // Parse WebSocket settings
        config_.websocket.host = SimpleJSON::GetStringValue(json_content, "websocket_host");
        config_.websocket.port = SimpleJSON::GetIntValue(json_content, "websocket_port");

        config_dirty_ = false;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error parsing config JSON: " << e.what() << std::endl;
        return false;
    }
}

std::string ConfigManager::SaveToJSON() const {
    std::stringstream json;
    json << "{\n";

    // Keyboard settings
    json << "  \"keyboard_auto_layout\": " << (config_.keyboard.auto_layout ? "true" : "false") << ",\n";
    json << "  \"keyboard_margin\": " << config_.keyboard.keyboard_margin << ",\n";
    json << "  \"white_key_width\": " << config_.keyboard.white_key_width << ",\n";
    json << "  \"white_key_height\": " << config_.keyboard.white_key_height << ",\n";
    json << "  \"black_key_width\": " << config_.keyboard.black_key_width << ",\n";
    json << "  \"black_key_height\": " << config_.keyboard.black_key_height << ",\n";

    // Audio settings
    json << "  \"audio_enabled\": " << (config_.audio.audio_enabled ? "true" : "false") << ",\n";
    json << "  \"audio_volume\": " << config_.audio.volume << ",\n";
    json << "  \"soundfont_path\": \"" << SimpleJSON::EscapeString(config_.audio.soundfont_path) << "\",\n";
    json << "  \"audio_polyphony\": " << config_.audio.polyphony << ",\n";
    json << "  \"limiter_enabled\": " << (config_.audio.limiter_enabled ? "true" : "false") << ",\n";

    // Audio limiter settings
    json << "  \"limiter_threshold\": " << config_.audio.limiter_threshold << ",\n";
    json << "  \"limiter_release_time\": " << config_.audio.limiter_release_time << ",\n";
    json << "  \"limiter_lookahead_time\": " << config_.audio.limiter_lookahead_time << ",\n";

    // BASS FX settings
    json << "  \"bassfx_enabled\": " << (config_.audio.bassfx_enabled ? "true" : "false") << ",\n";
    json << "  \"bassfx_reverb_enabled\": " << (config_.audio.bassfx_reverb_enabled ? "true" : "false") << ",\n";
    json << "  \"bassfx_chorus_enabled\": " << (config_.audio.bassfx_chorus_enabled ? "true" : "false") << ",\n";
    json << "  \"bassfx_echo_enabled\": " << (config_.audio.bassfx_echo_enabled ? "true" : "false") << ",\n";
    json << "  \"bassfx_compressor_enabled\": " << (config_.audio.bassfx_compressor_enabled ? "true" : "false") << ",\n";
    json << "  \"bassfx_reverb_mix\": " << config_.audio.bassfx_reverb_mix << ",\n";
    json << "  \"bassfx_chorus_mix\": " << config_.audio.bassfx_chorus_mix << ",\n";
    json << "  \"bassfx_echo_mix\": " << config_.audio.bassfx_echo_mix << ",\n";
    json << "  \"bassfx_compressor_ratio\": " << config_.audio.bassfx_compressor_ratio << ",\n";

    // Multiple soundfont settings
    json << "  \"use_multiple_soundfonts\": " << (config_.audio.use_multiple_soundfonts ? "true" : "false") << ",\n";
    json << "  \"soundfont_paths\": [";
    for (size_t i = 0; i < config_.audio.soundfont_paths.size(); ++i) {
        if (i > 0) json << ", ";
        json << "\"" << SimpleJSON::EscapeString(config_.audio.soundfont_paths[i]) << "\"";
    }
    json << "],\n";
    json << "  \"soundfont_enabled\": [";
    for (size_t i = 0; i < config_.audio.soundfont_enabled.size(); ++i) {
        if (i > 0) json << ", ";
        json << (config_.audio.soundfont_enabled[i] ? "true" : "false");
    }
    json << "],\n";
    json << "  \"soundfont_priorities\": [";
    for (size_t i = 0; i < config_.audio.soundfont_priorities.size(); ++i) {
        if (i > 0) json << ", ";
        json << config_.audio.soundfont_priorities[i];
    }
    json << "],\n";
    json << "  \"soundfont_volumes\": [";
    for (size_t i = 0; i < config_.audio.soundfont_volumes.size(); ++i) {
        if (i > 0) json << ", ";
        json << config_.audio.soundfont_volumes[i];
    }
    json << "],\n";
    json << "  \"soundfont_should_load\": [";
    for (size_t i = 0; i < config_.audio.soundfont_should_load.size(); ++i) {
        if (i > 0) json << ", ";
        json << (config_.audio.soundfont_should_load[i] ? "true" : "false");
    }
    json << "],\n";
    json << "  \"limiter_lookahead_time\": " << config_.audio.limiter_lookahead_time << ",\n";

    // Display settings
    json << "  \"bg_color_r\": " << config_.display.background_color[0] << ",\n";
    json << "  \"bg_color_g\": " << config_.display.background_color[1] << ",\n";
    json << "  \"bg_color_b\": " << config_.display.background_color[2] << ",\n";
    json << "  \"background_mode\": " << config_.display.background_mode << ",\n";
    json << "  \"gradient_center_r\": " << config_.display.gradient_center_color[0] << ",\n";
    json << "  \"gradient_center_g\": " << config_.display.gradient_center_color[1] << ",\n";
    json << "  \"gradient_center_b\": " << config_.display.gradient_center_color[2] << ",\n";
    json << "  \"gradient_edge_r\": " << config_.display.gradient_edge_color[0] << ",\n";
    json << "  \"gradient_edge_g\": " << config_.display.gradient_edge_color[1] << ",\n";
    json << "  \"gradient_edge_b\": " << config_.display.gradient_edge_color[2] << ",\n";

    json << "  \"background_image_path\": \"" << config_.display.background_image_path << "\",\n";
    json << "  \"background_image_opacity\": " << config_.display.background_image_opacity << ",\n";
    json << "  \"background_image_scale_mode\": " << config_.display.background_image_scale_mode << ",\n";
    json << "  \"show_settings\": " << (config_.display.show_settings ? "true" : "false") << ",\n";
    json << "  \"show_debug\": " << (config_.display.show_debug ? "true" : "false") << ",\n";
    json << "  \"show_bassmidi_status\": " << (config_.display.show_bassmidi_status ? "true" : "false") << ",\n";
    json << "  \"show_midi_input\": " << (config_.display.show_midi_input ? "true" : "false") << ",\n";
    json << "  \"show_audio_limiter\": " << (config_.display.show_audio_limiter ? "true" : "false") << ",\n";
    json << "  \"show_soundfont_manager\": " << (config_.display.show_soundfont_manager ? "true" : "false") << ",\n";
    json << "  \"show_notes_count_status\": " << (config_.display.show_notes_count_status ? "true" : "false") << ",\n";
    json << "  \"show_bassmidi_status_overlay\": " << (config_.display.show_bassmidi_status_overlay ? "true" : "false") << ",\n";

    // Note indicator settings
    json << "  \"note_indicator_enabled\": " << (config_.display.note_indicator_enabled ? "true" : "false") << ",\n";
    json << "  \"note_indicator_color_r\": " << config_.display.note_indicator_color[0] << ",\n";
    json << "  \"note_indicator_color_g\": " << config_.display.note_indicator_color[1] << ",\n";
    json << "  \"note_indicator_color_b\": " << config_.display.note_indicator_color[2] << ",\n";
    json << "  \"note_indicator_color_a\": " << config_.display.note_indicator_color[3] << ",\n";
    json << "  \"note_indicator_size_w\": " << config_.display.note_indicator_size[0] << ",\n";
    json << "  \"note_indicator_size_h\": " << config_.display.note_indicator_size[1] << ",\n";
    json << "  \"note_indicator_position_x\": " << config_.display.note_indicator_position[0] << ",\n";
    json << "  \"note_indicator_position_y\": " << config_.display.note_indicator_position[1] << ",\n";
    json << "  \"note_indicator_animation_duration\": " << config_.display.note_indicator_animation_duration << ",\n";
    json << "  \"note_indicator_bounce_distance\": " << config_.display.note_indicator_bounce_distance << ",\n";

    // MIDI settings
    json << "  \"selected_midi_device\": " << config_.midi.selected_midi_device << ",\n";
    json << "  \"selected_alsa_midi_device\": " << config_.midi.selected_alsa_midi_device << ",\n";
    json << "  \"use_alsa_midi\": " << (config_.midi.use_alsa_midi ? "true" : "false") << ",\n";
    json << "  \"auto_open_midi\": " << (config_.midi.auto_open_midi ? "true" : "false") << ",\n";

    // External Process MIDI settings
    json << "  \"use_ext_process_midi\": " << (config_.midi.use_ext_process_midi ? "true" : "false") << ",\n";
    json << "  \"ext_process_executable_path\": \"" << SimpleJSON::EscapeString(config_.midi.ext_process_executable_path) << "\",\n";
    std::string escaped_args = SimpleJSON::EscapeString(config_.midi.ext_process_arguments);
    // Debug output to check for issues
    std::cout << "DEBUG: Original args: [" << config_.midi.ext_process_arguments << "]" << std::endl;
    std::cout << "DEBUG: Escaped args: [" << escaped_args << "]" << std::endl;
    json << "  \"ext_process_arguments\": \"" << escaped_args << "\",\n";
    json << "  \"ext_process_enabled\": " << (config_.midi.ext_process_enabled ? "true" : "false") << ",\n";
    json << "  \"midi_color_type\": " << config_.midi.color_type << ",\n";

    // Window settings
    json << "  \"window_width\": " << config_.window.width << ",\n";
    json << "  \"window_height\": " << config_.window.height << ",\n";
    json << "  \"window_maximized\": " << (config_.window.maximized ? "true" : "false") << ",\n";


    // Cheat tool settings
    json << "  \"show_cheat_tool\": " << (config_.cheat_tool.show_cheat_tool ? "true" : "false") << ",\n";
    json << "  \"multioctave_enabled\": " << (config_.cheat_tool.multioctave_enabled ? "true" : "false") << ",\n";
    json << "  \"multioctave_count\": " << config_.cheat_tool.multioctave_count << ",\n";
    json << "  \"multioctave_distance\": " << config_.cheat_tool.multioctave_distance << ",\n";
    json << "  \"pc_keyboard_transpose\": " << config_.cheat_tool.pc_keyboard_transpose << ",\n";
    json << "  \"pc_keyboard_octave\": " << config_.cheat_tool.pc_keyboard_octave << ",\n";

    // WebSocket settings
    json << "  \"websocket_host\": \"" << SimpleJSON::EscapeString(config_.websocket.host) << "\",\n";
    json << "  \"websocket_port\": " << config_.websocket.port << "\n";

    json << "}";

    std::string result = json.str();

    // Debug: Check for malformed JSON in ext_process_arguments
    size_t args_pos = result.find("\"ext_process_arguments\":");
    if (args_pos != std::string::npos) {
        size_t line_start = result.rfind('\n', args_pos);
        size_t line_end = result.find('\n', args_pos);
        if (line_start == std::string::npos) line_start = 0;
        if (line_end == std::string::npos) line_end = result.length();
        std::string line = result.substr(line_start, line_end - line_start);
        std::cout << "DEBUG: ext_process_arguments line: " << line << std::endl;
    }

    return result;
}

void ConfigManager::LoadSoundFontsToManager(SoundFontManager& manager) const {
    if (config_.audio.use_multiple_soundfonts) {
        // Load multiple soundfonts from config
        manager.DeserializeFromConfig(
            config_.audio.soundfont_paths,
            config_.audio.soundfont_enabled,
            config_.audio.soundfont_priorities,
            config_.audio.soundfont_volumes,
            config_.audio.soundfont_should_load
        );
    } else if (!config_.audio.soundfont_path.empty() && config_.audio.soundfont_path != "default.sf2") {
        // Load legacy single soundfont
        manager.ClearAllSoundFonts();
        manager.AddSoundFont(config_.audio.soundfont_path);
    }
}

void ConfigManager::SaveSoundFontsFromManager(const SoundFontManager& manager) {
    if (manager.HasSoundFonts()) {
        config_.audio.use_multiple_soundfonts = true;
        config_.audio.soundfont_paths = manager.SerializePaths();
        config_.audio.soundfont_enabled = manager.SerializeEnabledStates();
        config_.audio.soundfont_priorities = manager.SerializePriorities();
        config_.audio.soundfont_volumes = manager.SerializeVolumes();
        config_.audio.soundfont_should_load = manager.SerializeShouldLoadStates();

        // Update legacy soundfont path for backward compatibility
        auto enabled_soundfonts = manager.GetEnabledSoundFonts();
        if (!enabled_soundfonts.empty()) {
            config_.audio.soundfont_path = enabled_soundfonts[0]->path;
        }
    } else {
        config_.audio.use_multiple_soundfonts = false;
        config_.audio.soundfont_paths.clear();
        config_.audio.soundfont_enabled.clear();
        config_.audio.soundfont_priorities.clear();
        config_.audio.soundfont_volumes.clear();
        config_.audio.soundfont_should_load.clear();
        config_.audio.soundfont_path = "default.sf2";
    }

    MarkDirty();
}
